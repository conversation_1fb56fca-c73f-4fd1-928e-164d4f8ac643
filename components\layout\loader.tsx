import { cn } from "@/lib/utils"

interface LoaderProps {
  disabled?: boolean
  className?: string
  speed?: number
}

const Loader = ({ disabled = false, className, speed = 2 }: LoaderProps) => {
  return (
    <div className={cn("flex items-center justify-center p-8", className)}>
      <div
        className={cn(
          "text-primary size-64 relative overflow-clip",
          "bg-[length:300%_100%] bg-no-repeat bg-muted",
          "mask-contain mask-center mask-no-repeat",
          !disabled && "animate-sweep"
        )}
        style={
          {
            backgroundImage: `linear-gradient(160deg,
              transparent 40%,
              rgba(0, 0, 0, 0.4) 45%,
              rgba(0, 0, 0, 0.8) 50%,
              rgba(0, 0, 0, 0.4) 55%,
              transparent 60%
            )`,
            animationDuration: `${speed}s`,
            maskImage: "url('/images/stellar.svg')"
          } as React.CSSProperties
        }
      />
    </div>
  )
}

Loader.displayName = "Loader"

export { Loader }
