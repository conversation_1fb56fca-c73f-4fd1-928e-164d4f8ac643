{"name": "template", "description": "Production-Ready Next.js Template by <PERSON><PERSON>", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://stellar-glaze.com"}, "displayName": "<PERSON><PERSON>", "homepage": "https://stellar-glaze.com", "repository": {"type": "git", "url": "https://github.com/stellar-glaze/template.git"}, "bugs": {"url": "https://github.com/stellar-glaze/template/issues", "email": "<EMAIL>"}, "version": "1.0.0", "private": true, "license": "UNLICENSED", "keywords": ["next.js", "react", "typescript", "tailwindcss", "shadcn-ui", "template"], "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "preview": "next build && next start", "lint": "biome lint", "format": "biome format --write .", "check": "biome check --write --unsafe", "clean": "rm -rf .next out dist node_modules/.cache", "typecheck": "tsc --noEmit", "update": "bun update --latest", "analyze": "ANALYZE=true next build"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@mdx-js/loader": "^3.1.0", "@mdx-js/react": "^3.1.0", "@next/mdx": "^15.1.8", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@react-email/components": "^0.0.41", "@types/mdx": "^2.0.13", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "input-otp": "^1.4.2", "lucide-react": "^0.511.0", "motion": "^12.12.2", "next": "^15.3.3", "next-themes": "^0.4.6", "react": "^19.1.0", "react-day-picker": "9.7.0", "react-dom": "^19.1.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.56.4", "react-resizable-panels": "^3.0.2", "recharts": "^2.15.3", "resend": "^4.5.1", "sonner": "^2.0.3", "tailwind-merge": "^3.3.0", "uuid": "^11.1.0", "vaul": "^1.1.2", "zod": "^3.25.28"}, "devDependencies": {"@biomejs/biome": "^1.9.4", "@next/bundle-analyzer": "^15.1.8", "@tailwindcss/postcss": "^4.1.7", "@tailwindcss/typography": "^0.5.16", "@types/node": "^22.15.21", "@types/react": "^19.1.5", "@types/react-dom": "^19.1.5", "@types/uuid": "^10.0.0", "next-compose-plugins": "^2.2.1", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^4.1.7", "tw-animate-css": "^1.3.0", "typescript": "^5.8.3"}, "engines": {"node": ">=22.0.0", "bun": ">=1.2.0"}, "packageManager": "bun@1.2.0"}