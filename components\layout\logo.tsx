"use client"

import { cn } from "@/lib/utils"
import type { ComponentProps } from "react"

interface LogoProps extends ComponentProps<"svg"> {}

const Logo = ({ className, ...props }: LogoProps) => {
  return (
    <svg
      viewBox="0 0 256 256"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={cn("size-64 aspect-square text-foreground", className)}
      {...props}
    >
      <path
        d="M61.5756 116.379C64.5071 122.172 68.5563 127.071 73.6691 131.387C82.3739 138.734 94.0775 144.286 107.899 149.981L116.168 153.277L116.831 160.369L119.747 203.756C103.226 161.574 60.3688 128 0 128C28.3173 128 42.3028 124.211 61.5756 116.379Z"
        fill="currentColor"
      />
      <path
        d="M136.262 52.27C152.788 94.4342 195.643 128 256 128C227.28 128 213.025 131.897 193.605 139.957C190.798 135.098 187.118 130.705 182.647 126.672C173.936 118.815 162.244 112.342 148.33 106.252L139.832 102.723L139.175 95.6397L136.262 52.27Z"
        fill="currentColor"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M128.002 256C128.002 256 128.002 227.555 156.445 213.333C170.667 206.222 191.998 191.326 191.998 163.556C191.998 135.111 170.666 120.889 135.113 106.667L128.002 0C128.002 0 128.002 28.4444 99.5598 42.6667C85.3383 49.7778 63.5812 64.6771 64.0061 92.4444C64.5179 125.881 85.3381 135.111 120.892 149.333L128.002 256ZM120.892 149.333C120.892 149.333 132.403 152.732 132.959 152.858C137.13 153.809 141.362 154.497 145.633 155.078C155.185 156.373 164.3 157.738 171.788 164.559C175.48 168.413 177.731 173.418 177.779 178.866C177.648 187.054 175.008 193.319 169.449 199.11C170.454 197.187 170.821 192.408 170.242 188.159C168.997 179.028 156.715 171.787 142.979 163.69C135.535 159.302 127.664 154.662 120.892 149.333ZM86.553 56.8889C85.5479 58.8122 85.1806 63.5915 85.76 67.84C87.0053 76.972 99.2871 84.2127 113.023 92.3109C120.466 96.6987 128.336 101.338 135.108 106.667C135.108 106.667 123.599 103.268 123.043 103.141C118.872 102.191 114.64 101.502 110.369 100.921C100.817 99.6265 91.7017 98.2611 84.2143 91.44C80.5216 87.5867 78.2707 82.5816 78.2228 77.1332C78.3544 68.9453 80.9938 62.6803 86.553 56.8889Z"
        fill="currentColor"
      />
    </svg>
  )
}

Logo.displayName = "Logo"

export { Logo }
