@property --x {
  syntax: "<length-percentage>";
  initial-value: 0%;
}

.blog-sort {
  display: flex;
  border-radius: var(--radius-md);

  background-color: var(--card);
  background-blend-mode: overlay;
  box-shadow: 0 4px 30px var(--card);
  -webkit-backdrop-filter: blur(16px) saturate(140%);
  backdrop-filter: blur(16px) saturate(140%);

  overflow: hidden;
  position: relative;
  padding: var(--spacing-2);

  &[data-sort="asc"] {
    --x: calc(0% + var(--spacing-2));

    & button:first-child {
      color: var(--background);
    }

    & button:last-child {
      color: var(--foreground);
    }
  }

  &[data-sort="desc"] {
    --x: 50%;

    & button:first-child {
      color: var(--foreground);
    }

    & button:last-child {
      color: var(--background);
    }
  }

  &::before {
    content: "";
    position: absolute;
    display: flex;
    inset: 0;
    margin: auto 0;
    left: var(--x);
    width: 42%;
    aspect-ratio: 1;
    background-color: var(--foreground);
    border-radius: var(--radius-md);
    transition: all 0.2s ease;
    z-index: 1;
  }

  & button {
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
    padding: var(--spacing-2);
    background-color: transparent;
    color: var(--foreground);
    border-radius: var(--radius-md);
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background-color: var(--secondary);
    }

    & svg {
      size: var(--font-size-sm);
      z-index: 2;
      color: inherit;
    }
  }
}
