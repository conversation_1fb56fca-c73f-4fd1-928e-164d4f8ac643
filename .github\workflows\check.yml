name: Lint & Format

on:
  push:
    paths:
      - "**"
  pull_request:
    paths:
      - "**"

jobs:
  check:
    name: Check & Auto-fix
    runs-on: ubuntu-latest
    permissions:
      contents: write

    steps:
      - uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}

      - uses: oven-sh/setup-bun@v2
        with:
          bun-version: latest

      - name: Install dependencies
        run: bun install

      - name: Run Biome check and fix
        run: bun check

      - name: Commit formatting changes
        if: github.event_name == 'push'
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"
          git add .
          git diff --staged --quiet || git commit -m "style: auto-format code [skip ci]"
          git push
