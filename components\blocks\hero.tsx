import { Section } from "@/components/layout/section"
import { Button } from "@/components/ui/button"



const Hero = () => {
  return (
    <Section container className="items-start w-full min-h-screen">
      {/* logo */}
      <div>

      </div>
      <div className="mt-20 ">
        <h1 className="text-4xl md:text-5xl lg:text-[61.5px] font-medium mb-15">
          MODERN SOFTWARE
        </h1>

        <div className="relative w-screen -ml-[51vw] left-1/2 flex items-center mb-15">
          <svg viewBox="0 0 200 20" className="w-1/2 h-10 fex-shrink-0">
            <path
              d="M0 10 Q50 5 100 10 T200 10"
              stroke="black"
              strokeWidth="3"
              fill="none"
            />
          </svg>
          <h1 className="text-4xl md:text-5xl lg:text-[61.5px] font-medium -ml-12 whitespace-nowrap">
            FUTURE-EXPERIENCES
          </h1>
        </div>

        <h1 className="text-4xl md:text-5xl lg:text-[61.5px] font-medium mb-15">
          EXCEPTIONAL DESIGN
        </h1>
        <p className="text-lg md:text-xl lg:text-xl mb-8">
          We Craft Tailored Web, Mobile, And Al Solutions - <br />
          For Brands That Want To Lead, Not Follow.
        </p>
        <Button className="bg-black text-white text-[18.39px] w-[180px] px-10 py-6 rounded-tr-[29.42px] rounded-bl-[29.42px] ">
          CONTACT
        </Button>
      </div>
    </Section>
  )
}

export { Hero }
