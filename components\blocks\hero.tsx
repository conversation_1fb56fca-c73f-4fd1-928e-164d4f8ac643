import { Section } from "@/components/layout/section"
import { Button } from "@/components/ui/button"
import { Background } from "@/components/layout/background"


const Hero = () => {
  return (
    <Section container className="items-start w-full min-h-screen">
      <Background />
      <div className="mt-20 ">
        <h1 className="text-4xl md:text-5xl lg:text-[61.5px] font-medium mb-15">
          MODERN SOFTWARE
        </h1>
        <h1 className="text-4xl md:text-5xl lg:text-[61.5px] font-medium mb-15">
          FUTURE-EXPERIENCES 
        </h1>
        <h1 className="text-4xl md:text-5xl lg:text-[61.5px] font-medium mb-15">
          EXCEPTIONAL DESIGN
        </h1>
        <p className="text-lg md:text-xl lg:text-xl mb-8">
          We Craft Tailored Web, Mobile, And Al Solutions - <br />
          For Brands That Want To Lead, Not Follow.
        </p>
        <Button className="bg-black text-white text-[18.39px] w-[180px] px-10 py-6 rounded-tr-[29.42px] rounded-bl-[29.42px] ">
          CONTACT
        </Button>
      </div>
    </Section>
  )
}

export { Hero }
