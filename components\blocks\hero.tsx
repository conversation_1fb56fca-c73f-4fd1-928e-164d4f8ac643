import { Section } from "@/components/layout/section"
import { Button } from "@/components/ui/button"

const DEFAULT_TITLE = {
  line1: 'MODERN SOFTWARE',
  line2: 'FUTURE-EXPERIENCES',
  line3: 'EXCEPTIONAL DESIGN'
}

const DEFAULT_DESCRIPTION = 'We Craft Tailored Web, Mobile, And Al Solutions - \nFor Brands That Want To Lead, Not Follow.'

const Hero = () => {
  return (
    <Section container className="items-start w-full min-h-screen relative">
      {/* logo */}
      <div className="absolute w-[88px] h-[83.52px] top-[54.1px] left-0">
        <img src="/images/logo.svg" alt="Logo" className="w-full h-full object-contain" />
      </div>
      {/* text */}
      <div className="mt-63">
        <h1 className="text-4xl md:text-5xl lg:text-[61.5px] font-medium mb-15">
          MODERN SOFTWARE
        </h1>

        <div className="relative w-screen -ml-[51vw] left-1/2 flex items-center mb-15">
          <svg viewBox="0 0 200 20" className="w-1/2 h-10 fex-shrink-0">
            <path
              d="M0 10 Q50 5 100 10 T200 10"
              stroke="black"
              strokeWidth="3"
              fill="none"
            />
          </svg>
          <h1 className="text-4xl md:text-5xl lg:text-[61.5px] font-medium -ml-12 whitespace-nowrap">
            FUTURE-EXPERIENCES
          </h1>
        </div>

        <h1 className="text-4xl md:text-5xl lg:text-[61.5px] font-medium mb-15">
          EXCEPTIONAL DESIGN
        </h1>
        <p className="text-lg md:text-xl lg:text-xl mb-8">
          We Craft Tailored Web, Mobile, And Al Solutions - <br />
          For Brands That Want To Lead, Not Follow.
        </p>
        {/* button */}
        <div className="flex items-center justify-between mt-20">
          <div>
            <Button className="bg-black text-white text-[18.39px] w-[205px] h-[59.42px] pt-[14.71px] pr-[22.06px] pb-[14.71px] pl-[10px] rounded-tr-[29.42px] rounded-bl-[29.42px] rounded-tl-none rounded-br-none flex items-center justify-center gap-3">
              CONTACT
              <span className="text-[33px] bottom-2 left-36 absolute">››</span>
            </Button>
          </div>
          <div className="absolute top-190 right-0">
            <Button className="bg-black text-white text-[18.39px] w-[93px] h-[59.42px] px-[16px] py-[12px] rounded-tl-[29.42px] rounded-br-[29.42px] rounded-tr-none rounded-bl-none flex items-center justify-center gap-3">
              work
            </Button>
            <Button className="bg-black text-white mt-8 text-[18.39px] w-[93px] h-[59.42px] px-[16px] py-[12px] rounded-tr-[29.42px] rounded-bl-[29.42px] rounded-tl-none rounded-br-none flex items-center justify-center gap-3">
              work
            </Button>
          </div>
        </div>
      </div>
    </Section>
  )
}

export { Hero }
