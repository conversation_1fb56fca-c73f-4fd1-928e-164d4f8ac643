import {
  GithubIcon,
  InstagramIcon,
  LinkedinIcon,
  TwitterIcon
} from "@/components/icons"
import { siteConfig } from "@/config/site"

const socials = [
  { label: "Twitter", href: siteConfig.socials.twitter, icon: TwitterIcon },
  { label: "LinkedIn", href: siteConfig.socials.linkedin, icon: LinkedinIcon },
  { label: "GitHub", href: siteConfig.socials.github, icon: GithubIcon },
  {
    label: "Instagram",
    href: siteConfig.socials.instagram,
    icon: InstagramIcon
  }
] as const

export const SocialLinks = () => {
  return (
    <div className="absolute bottom-4 sm:bottom-8 md:bottom-16 right-4 sm:right-8 md:right-16 group flex flex-col gap-2 sm:gap-3 md:gap-4 z-10">
      {socials.map(({ label, href, icon: Icon }) => (
        <a
          key={label}
          href={href}
          target="_blank"
          rel="noopener noreferrer"
          aria-label={label}
          className="text-foreground hover:text-foreground"
        >
          <Icon size={32} />
        </a>
      ))}
    </div>
  )
}
