import { siteConfig } from "@/config/site"
import { ImageResponse } from "next/og"

export const runtime = "edge"

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url)
  const hasTitle = searchParams.has("title")
  const title = hasTitle ? searchParams.get("title") : siteConfig.name
  const hasAuthor = searchParams.has("author")
  const author = hasAuthor
    ? searchParams.get("author")
    : siteConfig.company.members[0].name

  // Load font from CDN
  const fontData = await fetch(
    new URL("@/public/fonts/bord.ttf", import.meta.url)
  ).then((res) => res.arrayBuffer())

  // Load background image
  const imageData = await fetch(
    new URL("@/public/images/og-img.svg", import.meta.url)
  ).then((res) => res.arrayBuffer())

  return new ImageResponse(
    <div
      style={{
        width: "100%",
        height: "100%",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        ...(imageData
          ? {
              backgroundImage: `url(data:image/svg+xml;base64,${btoa(
                String.fromCharCode(...new Uint8Array(imageData))
              )})`,
              backgroundSize: "contain",
              backgroundPosition: "center",
              backgroundRepeat: "no-repeat"
            }
          : {
              backgroundColor: "#fff"
            }),
        color: "#000",
        fontSize: 64,
        lineHeight: "1.5",
        fontFamily: "Bord",
        padding: "4rem",
        textAlign: "start"
      }}
    >
      {title}
    </div>,
    {
      width: 1200,
      height: 630,
      fonts: [
        {
          name: "Bord",
          data: fontData,
          weight: 700,
          style: "normal"
        }
      ],
      status: 200,
      statusText: "OK",
      headers: {
        "Content-Type": "image/png"
      }
    }
  )
}
