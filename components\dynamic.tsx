import dynamic from "next/dynamic"
import { type ComponentType, Suspense } from "react"

interface DynamicProps {
  component: () => Promise<{ default: ComponentType<any> }>
  fallback?: React.ReactNode
  ssr?: boolean
}

const Dynamic = ({ component, fallback = null, ssr = false }: DynamicProps) => {
  const DynamicComponent = dynamic(component, {
    ssr,
    loading: () => <>{fallback}</>
  })

  return (
    <Suspense fallback={fallback}>
      <DynamicComponent />
    </Suspense>
  )
}

Dynamic.displayName = "Dynamic"

export { Dynamic }
