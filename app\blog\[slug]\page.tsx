import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator
} from "@/components/ui/breadcrumb"
import { Button } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { siteConfig } from "@/config/site"
import { cn } from "@/lib/utils"
import type { Frontmatter } from "@/types"
import { getFrontmatter, getSlugs } from "@/utils/server"
import { Clock, Share2 } from "lucide-react"
import type { Metadata } from "next"
import Image from "next/image"

export async function generateMetadata({
  params
}: { params: Promise<{ slug: string }> }) {
  const { slug } = await params
  const { frontmatter } = await getFrontmatter(slug)

  const { title, date, summary, author, image }: Frontmatter = frontmatter
  const ogImage = image
    ? image
    : `${siteConfig.url}/api/og?title=${encodeURIComponent(title ?? "Blog")}`

  const metadata: Metadata = {
    metadataBase: new URL(siteConfig.url),
    title: `${title} | Blog by ${author}`,
    description: summary,
    openGraph: {
      title,
      description: summary,
      type: "article",
      publishedTime: date,
      url: `${siteConfig.url}/blog/${slug}`,
      images: [
        {
          url: ogImage,
          width: 1200,
          height: 630,
          alt: ""
        }
      ]
    },
    twitter: {
      card: "summary_large_image",
      title,
      description: summary,
      images: [ogImage]
    }
  }

  return metadata
}

export default async function BlogSlugPage({
  params
}: {
  params: Promise<{ slug: string }>
}) {
  const { slug } = await params
  const { default: Post, frontmatter } = await import(`@/content/${slug}.mdx`)

  if (!frontmatter) {
    throw new Error("Missing frontmatter")
  }

  const readingTime = Math.ceil(Post.toString().split(/\s+/).length / 200) // Rough estimate: 200 words per minute

  return (
    <article className="border-x border-border my-16 font-sub">
      <div className="relative w-full mx-auto max-w-4xl p-8 border-x border-border">
        <Breadcrumb className="my-8 self-start">
          <BreadcrumbList className="m-0 list-none">
            <BreadcrumbItem>
              <BreadcrumbLink href="/blog">Blog</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>{frontmatter.title}</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
        <div className="mb-8 space-y-4">
          <div className="flex items-center gap-4">
            <Avatar className="h-10 w-10">
              <AvatarImage
                src={`/avatars/${frontmatter.author.toLowerCase().replace(/\s/g, "-")}.png`}
                alt={frontmatter.author}
              />
              <AvatarFallback>{frontmatter.author[0]}</AvatarFallback>
            </Avatar>
            <div className="flex flex-col">
              <span className="font-medium">{frontmatter.author}</span>
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <time>{frontmatter.date}</time>
                <span>•</span>
                <span className="flex items-center gap-1">
                  <Clock className="h-4 w-4" />
                  {readingTime}min read
                </span>
              </div>
            </div>
            <Button variant="ghost" size="icon" className="ml-auto">
              <Share2 className="h-4 w-4" />
            </Button>
          </div>

          {frontmatter.image && (
            <div className="relative aspect-video w-full overflow-hidden rounded-lg">
              <Image
                src={frontmatter.image}
                alt={frontmatter.title}
                fill
                className="object-cover"
                priority
              />
            </div>
          )}

          <p className="text-lg text-muted-foreground">{frontmatter.summary}</p>
        </div>

        <Separator className="my-8" />

        <div
          className={cn(
            "prose prose-neutral prose-headings:mt-8 prose-headings:font-semibold prose-headings:text-foreground prose-headings:font-sub",
            "prose-h1:text-5xl prose-h2:text-4xl prose-h3:text-3xl prose-h4:text-2xl prose-h5:text-xl prose-h6:text-lg",
            "dark:prose-invert max-w-none"
          )}
        >
          <Post />
        </div>
      </div>

      {/* Schema.org JSON-LD for Google Search engine */}
      <script
        type="application/ld+json"
        suppressHydrationWarning
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "BlogPosting",
            headline: frontmatter.title,
            datePublished: frontmatter.date,
            dateModified: frontmatter.date,
            description: frontmatter.summary,
            image: frontmatter.image
              ? `${siteConfig.url}${frontmatter.image}`
              : `${siteConfig.url}/api/og?title=${encodeURIComponent(frontmatter.title)}`,
            url: `${siteConfig.url}/blog/${slug}`,
            author: {
              "@type": "Person",
              name: frontmatter.author
            }
          })
        }}
      />
    </article>
  )
}

export async function generateStaticParams() {
  return getSlugs()
}

export const dynamicParams = false
