<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 768 768">
  <defs>
    <filter id="noise-filter" x="-50%" y="-50%" width="200%" height="200%" filterUnits="objectBoundingBox" primitiveUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feTurbulence type="turbulence" baseFrequency="0.2" numOctaves="4" seed="12" stitchTiles="stitch" x="0%" y="0%" width="100%" height="100%" result="turbulence"></feTurbulence>
      <feSpecularLighting surfaceScale="40" specularConstant="0.8" specularExponent="20" lighting-color="var(--accent)" x="0%" y="0%" width="100%" height="100%" in="turbulence" result="specularLighting">
        <feDistantLight azimuth="4" elevation="100"></feDistantLight>
      </feSpecularLighting>
      <feColorMatrix type="saturate" values="0" x="0%" y="0%" width="100%" height="100%" in="specularLighting" result="colormatrix"></feColorMatrix>
      <feComponentTransfer>
        <feFuncA type="linear" slope="0.4"/>
      </feComponentTransfer>
    </filter>
  </defs>
  <rect width="100%" height="100%" fill="transparent" filter="url(#noise-filter)"></rect>
</svg>