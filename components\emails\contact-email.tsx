import { env } from "@/config/env"
import { siteConfig } from "@/config/site"
import {
  <PERSON>,
  <PERSON><PERSON>,
  Container,
  Head,
  Heading,
  Hr,
  Html,
  Img,
  Link,
  Preview,
  Section,
  Tailwind,
  Text
} from "@react-email/components"

interface ContactEmailProps {
  name: string
  email: string
  message: string
}

const ASSET_URL =
  env.NODE_ENV === "production" ? siteConfig.url : env.NEXT_PUBLIC_BLOB_URL

export default function ContactEmail({
  name,
  email,
  message
}: ContactEmailProps) {
  return (
    <Html>
      <Head />
      <Preview>Thank you for reaching out to {siteConfig.name}</Preview>
      <Tailwind
        config={{
          theme: {
            extend: {
              colors: {
                background: "#fff",
                foreground: "#000",
                border: "#999",
                muted: "#666"
              }
            }
          }
        }}
      >
        <Body className="bg-background font-sans m-0 p-0">
          <Section className="relative mb-10">
            <Img
              src={`${ASSET_URL}/images/banner.webp`}
              width="100%"
              height="100px"
              alt={siteConfig.name}
            />

            <div className="absolute z-10 p-2 bg-background rounded-lg top-10 left-10">
              <Img
                src={`${ASSET_URL}/images/logo.webp`}
                width={100}
                height={100}
                alt={siteConfig.name}
              />
            </div>
          </Section>

          <Container className="rounded-lg bg-background p-10 mx-auto mt-16">
            <Heading className="m-0 mb-6 text-center text-2xl font-bold text-forground">
              Thank You for Reaching Out
            </Heading>

            <Text className="my-4 text-foreground">Hi {name},</Text>

            <Text className="my-4 text-foreground">
              Thank you for contacting <strong>{siteConfig.name}</strong>. We've
              received your message and appreciate you taking the time to reach
              out. Our team is dedicated to providing exceptional digital
              solutions, and we'll review your inquiry with care.
            </Text>

            <Section className="my-8 rounded-md bg-background p-6">
              <Text className="m-0 text-sm text-muted">Your message:</Text>
              <Text className="mt-2 whitespace-pre-wrap text-foreground">
                {message}
              </Text>
            </Section>

            <Text className="my-4 text-foreground">
              We aim to respond to all inquiries within 24-48 hours. In the
              meantime, you can explore our services and recent work on our
              website.
            </Text>

            <Section className="text-center mt-6">
              <Button
                className="rounded-tr-xl rounded-bl-xl bg-background px-6 py-3 text-background"
                href={siteConfig.url}
              >
                Visit Our Website
              </Button>
            </Section>

            <Hr className="my-6 border-border" />

            <Text className="text-sm text-muted">
              Best,
              <br />
              Team | {siteConfig.name}
              <br />
              {siteConfig.email}
            </Text>
          </Container>

          <Section className="py-8 text-center text-xs text-background bg-foreground">
            <Text>
              {siteConfig.company.legalName}
              <br />
              {siteConfig.company.location}
            </Text>
            <Text>
              <Link
                href={`${siteConfig.url}/unsubscribe?email=${email}`}
                className="text-muted underline"
              >
                Unsubscribe
              </Link>
            </Text>
          </Section>
        </Body>
      </Tailwind>
    </Html>
  )
}
