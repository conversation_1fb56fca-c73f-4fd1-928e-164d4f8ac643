"use client"

import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group"
import { ChevronDown, ChevronUp } from "lucide-react"
import { useRouter, useSearchParams } from "next/navigation"

import styles from "./index.module.css"

const BlogSort = () => {
  const router = useRouter()
  const searchParams = useSearchParams()
  const sort = searchParams.get("sort") || "asc"

  const handleSort = (newSort: "asc" | "desc") => {
    const params = new URLSearchParams(searchParams.toString())
    params.set("sort", newSort)
    router.replace(`?${params.toString()}`)
  }

  return (
    <ToggleGroup
      type="single"
      value={sort}
      onValueChange={(value) => value && handleSort(value as "asc" | "desc")}
      className={styles["blog-sort"]}
      data-sort={sort}
    >
      <ToggleGroupItem value="asc" aria-label="Sort by oldest first">
        <ChevronUp />
        <span className={styles.label}>Oldest</span>
      </ToggleGroupItem>
      <ToggleGroupItem value="desc" aria-label="Sort by newest first">
        <ChevronDown />
        <span className={styles.label}>Newest</span>
      </ToggleGroupItem>
    </ToggleGroup>
  )
}

export { BlogSort }
