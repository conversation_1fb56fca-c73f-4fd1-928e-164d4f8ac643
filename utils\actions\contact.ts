"use server"

import ContactEmail from "@/components/emails/contact-email"
import { env } from "@/config/env"
import { siteConfig } from "@/config/site"
import { contactSchema } from "@/lib/schemas"
import { render } from "@react-email/components"
import { Resend } from "resend"
import { v4 as uuidv4 } from "uuid"
import type { z } from "zod"

const resend = new Resend(env.RESEND_API_KEY)

type ContactFormValues = z.infer<typeof contactSchema>
type FormResponse = { status: "success" | "error"; message: string }

export async function sendMessage(
  data: ContactFormValues
): Promise<FormResponse> {
  // Validate the data
  const result = contactSchema.safeParse(data)

  if (!result.success) {
    return {
      status: "error",
      message: `Error: ${result.error.message}`
    }
  }

  try {
    const { name, email, message } = result.data

    await resend.emails.send({
      from: `Team | ${siteConfig.name} <${siteConfig.email}>`,
      to: [email],
      subject: `Thank you for reaching out to ${siteConfig.name}`,
      react: ContactEmail({ name, email, message }),
      text: await render(ContactEmail({ name, email, message }), {
        plainText: true
      }),
      headers: {
        "X-Entity-Ref-ID": uuidv4(),
        "List-Unsubscribe": `<${siteConfig.url}/unsubscribe?email=${email}>`
      },
      tags: [
        {
          name: "category",
          value: "contact_form"
        }
      ]
    })

    return {
      status: "success",
      message: "Thank you for your message! We'll get back to you soon."
    }
  } catch (error) {
    console.error("Failed to send email:", error)
    return {
      status: "error",
      message:
        error instanceof Error
          ? error.message
          : "Failed to send message. Please try again."
    }
  }
}
