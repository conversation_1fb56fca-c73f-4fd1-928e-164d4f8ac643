import { <PERSON>, <PERSON>o, Work_Sans } from "next/font/google"
import localFont from "next/font/local"

export const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
  display: "swap",
  preload: false // Not used in CSS
})

export const roboto = Roboto({
  variable: "--font-roboto",
  subsets: ["latin"],
  weight: ["400", "500", "700"],
  display: "swap",
  preload: false // Not used in CSS
})

export const work = Work_Sans({
  variable: "--font-work",
  subsets: ["latin"],
  display: "swap",
  preload: true // Used as --font-sub
})

export const bord = localFont({
  src: "../public/fonts/bord.ttf",
  variable: "--font-bord",
  display: "swap",
  preload: true // Used as --font-main
})
