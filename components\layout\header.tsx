"use client"

import { <PERSON> } from "@/components/layout/link"
/* import { Routes } from "@/config/constants" */
import { cn } from "@/lib/utils"
import Image from "next/image"
/* import { usePathname } from "next/navigation" */
import { useEffect, useState } from "react"

const Header = () => {
  /* const pathname = usePathname() */
  const [scrolled, setScrolled] = useState(false)

  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 0)
    }
    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  return (
    <header className={cn("glass-morph", scrolled && "shadow-md")}>
      <div data-container>
        <Link
          href="/"
          className="relative size-12 flex items-center hover:text-foreground hover:no-underline"
        >
          <Image
            src="/images/logo.svg"
            alt="Logo"
            fill
            className="aspect-square object-contain center font-main rounded-full bg-background"
          />
        </Link>
        {/* <nav className="flex flex-row gap-8">
          {Routes.map((route) => (
            <Link
              key={route.label}
              href={route.href}
              className={cn(
                "text-foreground/50 text-sm hover:text-foreground",
                pathname === route.href && "text-foreground"
              )}
            >
              {route.label}
            </Link>
          ))}
        </nav> */}
      </div>
    </header>
  )
}

export { Header }
