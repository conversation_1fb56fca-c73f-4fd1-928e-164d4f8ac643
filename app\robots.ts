import { siteConfig } from "@/config/site"
import type { MetadataRoute } from "next"

export default function robots(): MetadataRoute.Robots {
  return {
    rules: [
      {
        userAgent: "*",
        allow: ["/"],
        disallow: ["/temp/", "/api/"]
      }
      // {
      //   userAgent: "Googlebot",
      //   allow: ["/", "/_next/"]
      // }
      // {
      //   userAgent: "GPTBot",
      //   disallow: "/"
      // },
      // {
      //   userAgent: "ChatGPT-User",
      //   disallow: "/"
      // },
      // {
      //   userAgent: "CCBot",
      //   disallow: "/"
      // }
    ],
    sitemap: `${siteConfig.url}/sitemap.xml`,
    host: siteConfig.url
  }
}
