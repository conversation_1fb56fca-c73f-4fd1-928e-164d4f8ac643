import { Github, Instagram, Linkedin, Mail, Twitter } from "lucide-react"

import { siteConfig } from "@/config/site"

const iconMap = {
  github: <Github className="h-5 w-5" />,
  twitter: <Twitter className="h-5 w-5" />,
  instagram: <Instagram className="h-5 w-5" />,
  linkedin: <Linkedin className="h-5 w-5" />,
  email: <Mail className="h-5 w-5" />
} as const

const socials = [
  { label: "GitHub", href: siteConfig.socials.github, icon: "github" },
  { label: "Twitter", href: siteConfig.socials.twitter, icon: "twitter" },
  { label: "Instagram", href: siteConfig.socials.instagram, icon: "instagram" },
  { label: "LinkedIn", href: siteConfig.socials.linkedin, icon: "linkedin" },
  { label: "Email", href: `mailto:${siteConfig.email}`, icon: "email" }
] as const

const Footer = () => {
  return (
    <footer>
      <div data-container>
        <small className="text-foreground/50 text-sm">
          &copy; {new Date().getFullYear()}&nbsp;
          <span className="font-main text-xs text-foreground">
            {siteConfig.name}
          </span>
          . All rights reserved.
        </small>
        <div className="group flex flex-row gap-4">
          {socials.map(({ label, href, icon }) => (
            <a
              key={label}
              href={href}
              target="_blank"
              rel="noopener noreferrer"
              aria-label={label}
              className="text-foreground transition-colors group-hover:text-foreground/50 hover:text-foreground"
            >
              {iconMap[icon]}
            </a>
          ))}
        </div>
      </div>
    </footer>
  )
}

export { Footer }
