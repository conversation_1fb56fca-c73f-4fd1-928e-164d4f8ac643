"use client"

import { cn } from "@/lib/utils"
import { motion, useAnimation, useReducedMotion } from "motion/react"
import type { Variants } from "motion/react"
import type { ComponentProps } from "react"
import { forwardRef, useCallback, useImperativeHandle, useRef } from "react"

export interface TwitterIconHandle {
  startAnimation: () => void
  stopAnimation: () => void
}

interface TwitterIconProps extends ComponentProps<"svg"> {
  size?: number
}

const pathVariants: Variants = {
  normal: {
    opacity: 1,
    pathLength: 1,
    pathOffset: 0,
    transition: {
      duration: 0.2,
      ease: "easeOut",
      opacity: { duration: 0.1 }
    }
  },
  animate: {
    opacity: [0, 1],
    pathLength: [0, 1],
    pathOffset: [1, 0],
    transition: {
      duration: 0.3,
      ease: "easeInOut",
      opacity: { duration: 0.1 }
    }
  }
}

const TwitterIcon = forwardRef<TwitterIconHandle, TwitterIconProps>(
  ({ onMouseEnter, onMouseLeave, className, size = 28, ...props }, ref) => {
    const controls = useAnimation()
    const isControlledRef = useRef(false)
    const prefersReducedMotion = useReducedMotion()

    useImperativeHandle(ref, () => {
      isControlledRef.current = true

      return {
        startAnimation: () => {
          if (prefersReducedMotion) return
          controls.start("animate")
        },
        stopAnimation: () => controls.start("normal")
      }
    })

    const handleMouseEnter = useCallback(
      (e: React.MouseEvent<SVGSVGElement>) => {
        if (!isControlledRef.current) {
          if (prefersReducedMotion) return
          controls.start("animate")
        } else {
          onMouseEnter?.(e)
        }
      },
      [controls, onMouseEnter, prefersReducedMotion]
    )

    const handleMouseLeave = useCallback(
      (e: React.MouseEvent<SVGSVGElement>) => {
        if (!isControlledRef.current) {
          controls.start("normal")
        } else {
          onMouseLeave?.(e)
        }
      },
      [controls, onMouseLeave]
    )

    return (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        className={cn(className)}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        {...props}
      >
        <motion.path
          d="M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z"
          variants={pathVariants}
          initial="normal"
          animate={controls}
        />
      </svg>
    )
  }
)

TwitterIcon.displayName = "TwitterIcon"

export { TwitterIcon }
