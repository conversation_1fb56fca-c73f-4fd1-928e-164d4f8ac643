"use client"

import { Section } from "@/components/layout"
import { But<PERSON> } from "@/components/ui/button"
import { env } from "@/config/env"
import { AlertTriangle, RefreshCw } from "lucide-react"
import { useRouter } from "next/navigation"
import { useEffect } from "react"

export default function GlobalError({
  error,
  reset
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  const router = useRouter()

  useEffect(() => {
    // Log error to monitoring service
    console.error("Global application error:", error)

    // In production, send to error tracking service
    if (env.NODE_ENV === "production") {
      // Example: Sentry.captureException(error)
    }
  }, [error])

  return (
    <html lang="en">
      <body>
        <main>
          <Section
            container
            className="min-h-screen flex items-center justify-center"
          >
            <div className="text-center space-y-6 max-w-md">
              <AlertTriangle className="mx-auto h-16 w-16 text-destructive" />
              <div className="space-y-2">
                <h1 className="text-2xl font-bold">Something went wrong!</h1>
                <p className="text-muted-foreground">
                  We apologize for the inconvenience. An unexpected error has
                  occurred.
                </p>
                {env.NODE_ENV === "development" && (
                  <details className="mt-4 p-4 bg-muted rounded-lg text-left">
                    <summary className="cursor-pointer font-medium">
                      Error Details
                    </summary>
                    <pre className="mt-2 text-sm overflow-auto">
                      {error.message}
                      {error.stack && `\n\n${error.stack}`}
                    </pre>
                  </details>
                )}
              </div>
              <div className="flex gap-4 justify-center">
                <Button
                  onClick={() => reset()}
                  className="flex items-center gap-2"
                >
                  <RefreshCw className="h-4 w-4" />
                  Try again
                </Button>
                <Button variant="outline" onClick={() => router.push("/")}>
                  Go home
                </Button>
              </div>
            </div>
          </Section>
        </main>
      </body>
    </html>
  )
}
