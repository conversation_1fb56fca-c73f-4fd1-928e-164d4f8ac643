"use client"

import { cn } from "@/lib/utils"
import type { Route } from "next"
import NextLink, { type LinkProps as NextLinkProps } from "next/link"
import { useState } from "react"

interface LinkProps<T extends string> extends Omit<NextLinkProps, "href"> {
  href: Route<T> | URL
  className?: string
  children: React.ReactNode
}

const Link = <T extends string>({
  href,
  children,
  className,
  ...props
}: LinkProps<T>) => {
  const [isHovered, setIsHovered] = useState(false)

  return (
    <NextLink
      href={href}
      prefetch={isHovered}
      {...props}
      className={cn(
        "flex items-center justify-center text-center text-foreground hover:text-foreground/50 hover:no-underline",
        className
      )}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {children}
    </NextLink>
  )
}

Link.displayName = "Link"
export { Link }
