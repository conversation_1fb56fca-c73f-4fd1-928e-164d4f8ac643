"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList
} from "@/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger
} from "@/components/ui/popover"
import { ScrollArea } from "@/components/ui/scroll-area"
import { cn } from "@/lib/utils"
import { Check, ChevronsUpDown } from "lucide-react"
import * as React from "react"

interface ComboboxProps {
  items: { label: string; value: string }[]
  value?: string
  onSelect: (value: string) => void
  placeholder?: string
  className?: string
  buttonClassName?: string
  contentClassName?: string
  emptyMessage?: string
  searchPlaceholder?: string
}

const Combobox = ({
  items,
  value,
  onSelect,
  placeholder,
  className,
  buttonClassName,
  contentClassName,
  emptyMessage = "No results found.",
  searchPlaceholder
}: ComboboxProps) => {
  const [open, setOpen] = React.useState(false)

  const stopPropagation = React.useCallback((e: React.UIEvent) => {
    e.stopPropagation()
  }, [])

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn("w-full justify-between", buttonClassName)}
        >
          {value
            ? items.find((item) => item.value === value)?.label
            : placeholder || "Select..."}
          <ChevronsUpDown className="h-4 w-4 shrink-0 opacity-50 rtl:mr-2 ltr:ml-2" />
        </Button>
      </PopoverTrigger>
      <PopoverContent
        className={cn("w-full p-0", contentClassName)}
        onWheel={stopPropagation}
        onTouchMove={stopPropagation}
        align="start"
      >
        <Command className="rtl:text-right">
          <CommandInput
            placeholder={
              searchPlaceholder || `${placeholder?.toLowerCase()}...`
            }
            className="rtl:text-right rtl:pr-3 rtl:pl-8"
          />
          <CommandList>
            <CommandEmpty>{emptyMessage}</CommandEmpty>
            <ScrollArea
              className={cn(
                items.length > 6 ? "h-[264px]" : "h-fit max-h-[264px]"
              )}
            >
              <CommandGroup>
                {items.map((item) => (
                  <CommandItem
                    key={item.value}
                    value={item.label}
                    onSelect={() => {
                      onSelect(item.value)
                      setOpen(false)
                    }}
                    className="flex-row-reverse justify-between rtl:flex-row"
                  >
                    <Check
                      className={cn(
                        "h-4 w-4 opacity-0",
                        value === item.value && "opacity-100",
                        "rtl:ml-0 rtl:mr-2 ltr:mr-2"
                      )}
                    />
                    {item.label}
                  </CommandItem>
                ))}
              </CommandGroup>
            </ScrollArea>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  )
}

Combobox.displayName = "Combobox"

export { Combobox }
