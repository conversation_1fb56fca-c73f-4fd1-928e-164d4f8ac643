name: Branch Protection

on:
  push:
    branches: [dev, master, main]
  pull_request:
    types: [opened, edited, reopened]
    branches: [dev, master, main]

jobs:
  block-direct-push:
    name: Block Direct Push
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && github.actor != 'github-actions[bot]'
    steps:
      - name: Check if user is admin
        id: check-admin
        uses: actions/github-script@v7
        with:
          script: |
            try {
              const { data } = await github.rest.repos.getCollaboratorPermissionLevel({
                owner: context.repo.owner,
                repo: context.repo.repo,
                username: context.actor
              });
              return data.permission === 'admin';
            } catch (error) {
              return false;
            }

      - name: Block direct push (non-admins only)
        if: steps.check-admin.outputs.result == 'false'
        run: |
          echo "VIOLATION: Direct push detected to protected branch!"
          echo "This code should not be here. Please:"
          echo "1. Revert this commit"
          echo "2. Create a feature/ branch"
          echo "3. Submit a proper PR to dev"
          exit 1

  validate-pr:
    name: <PERSON><PERSON><PERSON>ull Request
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Check PR target branch
        run: |
          if [[ "${{ github.base_ref }}" != "dev" ]]; then
            echo "PRs must target 'dev' branch, not '${{ github.base_ref }}'"
            exit 1
          fi

      - name: Check source branch naming
        run: |
          if [[ ! "${{ github.head_ref }}" =~ ^feature/ ]]; then
            echo "Source branch must start with 'feature/', got '${{ github.head_ref }}'"
            exit 1
          fi

      - name: Validate commit messages
        run: |
          git fetch origin ${{ github.head_ref }}
          COMMITS=$(git log origin/dev..origin/${{ github.head_ref }} --pretty=format:"%s")
          while IFS= read -r commit; do
            if [[ ! "$commit" =~ ^(feat|fix|docs|style|refactor|test|chore)(\([a-z-]+\))?: ]]; then
              echo "Invalid commit: $commit"
              echo "Format: type(scope): description"
              echo "Types: feat, fix, docs, style, refactor, test, chore"
              exit 1
            fi
          done <<< "$COMMITS"
