import { Section } from "@/components/layout"
// import { BlogSort } from "./_components/blog-sort"
import { Spinner } from "@/components/ui/spinner"
import { getBlogs } from "@/utils/server"
import { Suspense } from "react"
import { BlogList } from "./_components/blog-list"

export default async function BlogPage({
  searchParams
}: {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>
}) {
  const { sort } = await searchParams

  const sortOrder = sort === "desc" ? "desc" : "asc" // default to 'asc'
  const blogs = getBlogs()

  return (
    <main>
      <Section container className="mt-16">
        <Suspense fallback={<Spinner variant="infinite" className="size-24" />}>
          <BlogList blogs={blogs} sortOrder={sortOrder} />
        </Suspense>
      </Section>
    </main>
  )
}
