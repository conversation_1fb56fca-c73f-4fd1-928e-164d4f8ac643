import { cn } from "@/lib/utils"
import type { ReactNode } from "react"

interface SectionProps {
  children: ReactNode
  className?: string
  container?: boolean
  id?: string
}

const Section = ({
  children,
  className,
  container = true,
  id
}: SectionProps) => {
  return (
    <section id={id}>
      {container ? (
        <div
          className={cn("container size-full flex flex-col grow", className)}
        >
          {children}
        </div>
      ) : (
        children
      )}
    </section>
  )
}

export { Section }
