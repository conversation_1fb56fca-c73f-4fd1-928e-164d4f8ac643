import { readFile } from "node:fs/promises"
import { join, resolve } from "node:path"
import { siteConfig } from "@/config/site"
import { ImageResponse } from "next/og"

// Image metadata
export const alt = siteConfig.name
export const size = {
  width: 1200,
  height: 630
}

export const contentType = "image/webp"

// Image generation
export default async function Image() {
  const publicDir = resolve("public")
  // Font loading, process.cwd() is Next.js project directory
  const fontData = await readFile(join(publicDir, "fonts/bord.ttf"))
  const imageData = await readFile(join(publicDir, "images/og-img.svg"))

  return new ImageResponse(
    // ImageResponse JSX element
    <div
      style={{
        width: "100%",
        height: "100%",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        ...(imageData
          ? {
              backgroundImage: `url(data:image/svg+xml;base64,${btoa(
                String.fromCharCode(...new Uint8Array(imageData))
              )})`,
              backgroundSize: "contain",
              backgroundPosition: "center",
              backgroundRepeat: "no-repeat"
            }
          : {
              backgroundColor: "#fff"
            }),
        color: "#000",
        fontSize: 75,
        lineHeight: "1.5",
        fontFamily: "Bord",
        padding: "4rem",
        textAlign: "start"
      }}
    >
      {siteConfig.name}
    </div>,
    // ImageResponse options
    {
      // For convenience, we can re-use the exported opengraph-image
      // size config to also set the ImageResponse's width and height.
      ...size,
      fonts: [
        {
          name: "Bord",
          data: fontData,
          style: "normal",
          weight: 700
        }
      ]
    }
  )
}
