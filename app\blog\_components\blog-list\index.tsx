"use client"

import { <PERSON> } from "@/components/layout/link"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardFooter, CardHeader } from "@/components/ui/card"
import { cn } from "@/lib/utils"
import type { Blog } from "@/types"
import { <PERSON>rk<PERSON> } from "lucide-react"
import Image from "next/image"
import { use } from "react"

interface BlogListProps {
  blogs: Promise<Blog[]>
  sortOrder: "asc" | "desc"
}

const BlogList = ({ blogs, sortOrder }: BlogListProps) => {
  const resolvedBlogs = use(blogs)

  function sortBlogs(blogs: Blog[], sortOrder: "asc" | "desc"): Blog[] {
    return blogs.sort((a, b) => {
      if (
        new Date(a.frontmatter.date || "") > new Date(b.frontmatter.date || "")
      ) {
        return sortOrder === "asc" ? -1 : 1
      }
      return sortOrder === "asc" ? 1 : -1
    })
  }

  return (
    <ul className="size-full list-none mx-0 my-16 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-0 gap-y-4 divide-x divide-y">
      {sortBlogs(resolvedBlogs, sortOrder).map((blog, i) => {
        const { title, date, author, summary, image } = blog.frontmatter

        return (
          <li
            key={blog.slug}
            className={cn(
              "relative",
              i % 3 === 0 && "border-l border-b border-border",
              i % 3 !== 0 && "border-r border-b border-border"
            )}
          >
            <Link
              href={`/blog/${blog.slug}`}
              className="block h-full focus:outline-none group"
            >
              <Card className="h-full border-0 rounded-none shadow-none hover:bg-muted/40 transition-colors">
                <CardHeader className="p-6 pb-2 flex flex-row items-center gap-3">
                  <Avatar className="w-8 h-8">
                    <AvatarImage
                      src={
                        author
                          ? `/avatars/${author.toLowerCase().replace(/\s/g, "-")}.png`
                          : undefined
                      }
                      alt={author}
                    />
                    <AvatarFallback>{author ? author[0] : "?"}</AvatarFallback>
                  </Avatar>
                  <span className="text-sm font-medium text-foreground">
                    {author}
                  </span>
                  <span className="text-xs text-muted-foreground ml-auto">
                    {date}
                  </span>
                </CardHeader>
                <CardContent>
                  <div className="relative w-full aspect-video bg-muted rounded overflow-hidden">
                    {image ? (
                      <Image
                        src={image}
                        alt={title || ""}
                        fill
                        quality={100}
                        className="object-cover"
                      />
                    ) : (
                      <div className="absolute inset-0 flex items-center justify-center bg-muted">
                        <Sparkles className="w-12 h-12" />
                      </div>
                    )}
                  </div>
                </CardContent>
                <CardFooter className="my-4 py-0">
                  <h3 className="text-start text-xl font-semibold mb-2 group-hover:text-primary transition-colors">
                    {title}
                  </h3>
                  {/* <p className="text-start text-base text-muted-foreground line-clamp-4">
                    {summary}
                  </p> */}
                </CardFooter>
              </Card>
            </Link>
          </li>
        )
      })}
    </ul>
  )
}

export { BlogList }
