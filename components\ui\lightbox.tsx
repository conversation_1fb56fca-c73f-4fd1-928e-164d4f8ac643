"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Dialog, DialogClose, DialogContent } from "@/components/ui/dialog"
import { cn } from "@/lib/utils"
import { ChevronLeft, ChevronRight, X } from "lucide-react"
import Image from "next/image"
import * as React from "react"

interface LightboxProps {
  images: { src: string; alt: string }[]
  initialIndex?: number
  onClose?: () => void
  className?: string
}

const Lightbox = ({
  images,
  initialIndex = 0,
  onClose,
  className
}: LightboxProps) => {
  const [currentIndex, setCurrentIndex] = React.useState(initialIndex)
  const [open, setOpen] = React.useState(true)

  const handleClose = () => {
    setOpen(false)
    onClose?.()
  }

  const handlePrevious = (e: React.MouseEvent) => {
    e.stopPropagation()
    setCurrentIndex((prev) => (prev === 0 ? images.length - 1 : prev - 1))
  }

  const handleNext = (e: React.MouseEvent) => {
    e.stopPropagation()
    setCurrentIndex((prev) => (prev === images.length - 1 ? 0 : prev + 1))
  }

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent
        className={cn("max-w-[90vw] max-h-[90vh] p-0 bg-black/90", className)}
      >
        <div className="relative w-full h-full flex items-center justify-center">
          <Image
            src={images[currentIndex]?.src ?? ""}
            alt={images[currentIndex]?.alt ?? ""}
            width={1200}
            height={800}
            className="max-h-[85vh] w-auto object-contain"
            priority
          />

          {images.length > 1 && (
            <>
              <Button
                variant="ghost"
                size="icon"
                className="absolute left-4 top-1/2 -translate-y-1/2 bg-black/50 hover:bg-black/70"
                onClick={handlePrevious}
              >
                <ChevronLeft className="h-6 w-6" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                className="absolute right-4 top-1/2 -translate-y-1/2 bg-black/50 hover:bg-black/70"
                onClick={handleNext}
              >
                <ChevronRight className="h-6 w-6" />
              </Button>
            </>
          )}

          <DialogClose className="absolute right-4 top-4 bg-black/50 hover:bg-black/70">
            <X className="h-6 w-6" />
          </DialogClose>
        </div>
      </DialogContent>
    </Dialog>
  )
}

export { Lightbox }
