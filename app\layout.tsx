import { siteConfig } from "@/config/site"
import { bord, work } from "@/lib/fonts"
import { Providers } from "@/providers"

import { Background, Footer, Header } from "@/components/layout"
import type { Metadata, Viewport } from "next"
import { headers } from "next/headers"
import Script from "next/script"

import "@/styles/globals.css"

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 5,
  userScalable: true,
  themeColor: [
    { media: "(prefers-color-scheme: light)", color: "#ffffff" },
    { media: "(prefers-color-scheme: dark)", color: "#000000" }
  ]
}

export const metadata: Metadata = {
  metadataBase: new URL(siteConfig.url),
  title: {
    default: siteConfig.title,
    template: `%s | ${siteConfig.name}`
  },
  description: siteConfig.description,
  keywords: [
    ...siteConfig.metadata.keywords,
    ...siteConfig.company.services,
    ...siteConfig.metadata.tags
  ],
  authors: [
    {
      name: siteConfig.company.founder.name,
      url: siteConfig.company.founder.website
    }
  ],
  creator: siteConfig.company.founder.name,
  publisher: siteConfig.company.name,
  formatDetection: {
    email: false,
    address: false,
    telephone: false
  },
  category: siteConfig.metadata.categories.join(", "),
  openGraph: {
    type: "website",
    locale: siteConfig.locales[0].locale,
    alternateLocale: siteConfig.locales.map((l) => l.locale),
    url: siteConfig.url,
    title: siteConfig.title,
    description: siteConfig.description,
    siteName: siteConfig.name,
    countryName: siteConfig.company.location,
    images: [
      {
        url: `${siteConfig.url}/opengraph-image`,
        width: 1200,
        height: 630,
        alt: siteConfig.title
      }
    ]
  },
  twitter: {
    card: "summary_large_image",
    title: siteConfig.title,
    description: siteConfig.description,
    images: [`${siteConfig.url}/opengraph-image`],
    creator: siteConfig.company.social.twitter.username,
    site: siteConfig.company.social.twitter.username
  },
  alternates: {
    canonical: siteConfig.url,
    languages: Object.fromEntries(
      siteConfig.locales.map((l) => [l.locale, "/"])
    )
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1
    }
  },
  applicationName: siteConfig.name,
  appleWebApp: {
    capable: true,
    title: siteConfig.name,
    statusBarStyle: "black-translucent"
  }
}

export default async function RootLayout({
  children
}: Readonly<{
  children: React.ReactNode
}>) {
  const nonce = (await headers()).get("x-nonce")

  const structuredData = {
    "@context": "https://schema.org",
    "@type": [
      "Organization",
      "WebSite",
      "SoftwareCompany",
      "ProfessionalService"
    ],
    name: siteConfig.company.name,
    description: siteConfig.description,
    url: siteConfig.url,
    logo: siteConfig.company.logo,
    foundingDate: siteConfig.company.founded.toString(),
    founder: {
      "@type": "Person",
      name: siteConfig.company.founder.name,
      url: siteConfig.company.founder.website
    },
    sameAs: [
      siteConfig.socials.twitter,
      siteConfig.socials.linkedin,
      siteConfig.socials.github,
      siteConfig.socials.instagram
    ],
    contactPoint: {
      "@type": "ContactPoint",
      email: siteConfig.email,
      contactType: "customer service"
    }
  }

  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <Script
          id="structured-data"
          type="application/ld+json"
          nonce={nonce ?? undefined}
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(structuredData)
          }}
        />
        <link
          rel="alternate"
          type="application/rss+xml"
          title="Stellar Glaze Blog RSS"
          href="/api/rss"
        />
      </head>
      <body
        className={`${work.variable} ${bord.variable} antialiased typography`}
      >
        <Providers>
          <Background />
          {/* <Header /> */}
          {children}
          <Footer />
        </Providers>
      </body>
    </html>
  )
}
