import { siteConfig } from "@/config/site"
import { getBlogs } from "@/utils/server"

export async function GET() {
  const allBlogs = await getBlogs()

  const itemsXml = allBlogs
    .sort((a, b) => {
      if (
        new Date(a.frontmatter.date || "") > new Date(b.frontmatter.date || "")
      ) {
        return -1
      }
      return 1
    })
    .map(
      (post) =>
        `<item>
          <title><![CDATA[${post.frontmatter.title}]]></title>
          <link>${siteConfig.url}/blog/${post.slug}</link>
          <description><![CDATA[${post.frontmatter.summary || ""}]]></description>
          <author><![CDATA[${post.frontmatter.author}]]></author>
          <pubDate>${new Date(
            post.frontmatter.date || ""
          ).toUTCString()}</pubDate>
          <guid>${siteConfig.url}/blog/${post.slug}</guid>
          ${post.frontmatter.image ? `<enclosure url="${siteConfig.url}${post.frontmatter.image}" type="image/jpeg"/>` : ""}
        </item>`
    )
    .join("\r\n")

  const rssFeed = `<?xml version="1.0" encoding="UTF-8" ?>
  <rss version="2.0" xmlns:atom="http://www.w3.org/2005/Atom">
    <channel>
        <title><![CDATA[${siteConfig.name} - Blog]]></title>
        <link>${siteConfig.url}</link>
        <description><![CDATA[${siteConfig.description}]]></description>
        <language>en-US</language>
        <managingEditor>${siteConfig.email} (${siteConfig.company.members[0].name})</managingEditor>
        <webMaster>${siteConfig.email} (${siteConfig.company.members[0].name})</webMaster>
        <lastBuildDate>${new Date().toUTCString()}</lastBuildDate>
        <pubDate>${new Date().toUTCString()}</pubDate>
        <ttl>60</ttl>
        <image>
          <url>${siteConfig.url}/opengraph-image</url>
          <title><![CDATA[${siteConfig.name}]]></title>
          <link>${siteConfig.url}</link>
        </image>
        <atom:link href="${siteConfig.url}/api/rss" rel="self" type="application/rss+xml" />
        ${itemsXml}
    </channel>
  </rss>`

  return new Response(rssFeed, {
    headers: {
      "Content-Type": "application/rss+xml; charset=utf-8"
    }
  })
}
