import { z } from "zod"

// Server-side environment variables
const serverSchema = z.object({
  NODE_ENV: z
    .enum(["development", "production", "test"])
    .default("development"),
  RESEND_API_KEY: z.string().optional(),
  BLOB_READ_WRITE_TOKEN: z.string().optional()
  // Add server-only env vars here
  // Example:
  // DATABASE_URL: z.string().url().optional(),
  // API_KEY: z.string().min(1).optional(),
})

// Client-side environment variables (must be prefixed with NEXT_PUBLIC_)
const clientSchema = z.object({
  NEXT_PUBLIC_APP_URL: z.string().url().default("http://localhost:3000"),
  NEXT_PUBLIC_API_URL: z.string().url().default("http://localhost:3000/api"),
  NEXT_PUBLIC_SELINE_TOKEN: z.string().optional(),
  NEXT_PUBLIC_BLOB_URL: z.string().optional()
  // Add client-safe env vars here
  // Example:
  // NEXT_PUBLIC_GA_ID: z.string().optional(),
})

// Combined schema
const envSchema = z.object({
  ...serverSchema.shape,
  ...clientSchema.shape
})

// Export validated env vars
export const env = (() => {
  try {
    const serverEnv = serverSchema.parse(process.env)
    const clientEnv = clientSchema.parse(process.env)

    return {
      ...serverEnv,
      ...clientEnv
    } as const
  } catch (error) {
    if (error instanceof z.ZodError) {
      const missingVars = error.errors
        .map((err) => err.path.join("."))
        .join(", ")
      throw new Error(
        `Invalid environment variables: ${missingVars}\nEnsure all required variables are set.`
      )
    }
    throw error
  }
})()

// Type-safe environment variables
declare global {
  namespace NodeJS {
    interface ProcessEnv extends z.infer<typeof envSchema> {}
  }
}
