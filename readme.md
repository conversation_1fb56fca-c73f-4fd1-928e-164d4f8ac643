# Stellar Glaze - Production-Ready Next.js Template

A modern, production-ready Next.js template with TypeScript, TailwindCSS, and best practices built-in.

## Tools

- **Next.js 15** with App Router and React 19
- **TypeScript** with strict configuration
- **TailwindCSS** with custom design system
- **Shadcn/ui** components with Radix UI primitives
- **MDX** support for content management
- **Biome** for fast linting and formatting
- **Prettier** – Code formatting (used for CSS only)

## Features

- **Type safety** with strict TypeScript configuration
- **Security headers** and middleware protection
- **SEO optimized** with metadata API
- **PWA ready** with manifest and service worker support
- **Error boundaries** and comprehensive error handling
- **Environment validation** with Zod
- **Theme switching** with TailwindCSS and Shadcn/ui
- **Performance optimized** with bundle analysis

## Getting Started

### Prerequisites

- Node.js 22+
- Bun 1.2+
- Git

### Installation

1. Clone the repository:

```bash
git clone <repo>
cd <repo>
```

2. Install dependencies:

```bash
bun install
```

3. Set up environment variables:

```bash
cp .env.example .env.local
cp .env.example .env.production
```

4. Start the development server:

```bash
bun dev
```

Open [http://localhost:3000](http://localhost:3000) to see your application.

## Project Structure

```
├── app/                   # Next.js App Router
│   ├── (sections)/        # Route groups
│   ├── api/               # API routes
│   ├── blog/              # Blog pages
│   ├── layout.tsx         # Root layout
│   ├── page.tsx           # Home page
│   ├── error.tsx          # Error boundary
│   ├── global-error.tsx   # Global error boundary
│   ├── loading.tsx        # Loading UI
│   ├── not-found.tsx      # 404 page
│   ├── manifest.ts        # PWA manifest
│   ├── robots.ts          # Robots.txt
│   └── sitemap.ts         # Sitemap
├── components/            # Reusable components
│   ├── ui/                # Shadcn/ui components
│   ├── layout/            # Layout components
│   ├── blocks/            # Page blocks
│   └── forms/             # Form components
├── config/                # Configuration files
│   ├── constants.ts       # Global constants
│   ├── site.ts            # Site configuration
│   └── env.ts             # Environment validation
├── content/               # MDX content files
├── hooks/                 # Custom React hooks
├── lib/                   # Utility libraries
│   ├── fonts.ts           # Font configuration
│   ├── schemas.ts         # Data validation
│   └── utils.ts           # Utility functions
├── providers/             # React context providers
├── styles/                # CSS and styling
│   ├── modules/           # CSS modules
│   └── globals.css        # Global styles with TailwindCSS
├── types/                 # TypeScript type definitions
├── utils/                 # Utility functions
│   ├── actions/           # Server actions
│   └── server.ts          # Server-side functions
└── public/                # Static assets
│   ├── font/              # Local font files
│   ├── images/            # Image assets
│   └── favicon.ico        # Favicon
```

## Available Scripts

```bash
# Development
bun run dev                   # Start development server
bun run build                 # Build for production
bun run start                 # Start production server
bun run preview               # Build and preview locally

# Code Quality
bun run check                 # Format and fix code

# Utilities
bun run clean                 # Clean build artifacts
bun run test                  # Run tests (when implemented)
```

## Configuration

### Environment Variables

Create a `.env.local` file with the following variables:

```env
# Required
NEXT_PUBLIC_APP_URL=https://your-domain.com
NEXT_PUBLIC_API_URL=https://your-domain.com/api

# Optional (add as needed)
# DATABASE_URL=
# AUTH_SECRET=
# SMTP_HOST=
# NEXT_PUBLIC_GA_ID=
```

### Site Configuration

Edit `config/site.ts` to customize your site:

```typescript
export const siteConfig = {
  name: "Your Site Name",
  description: "Your site description",
  url: "https://your-domain.com",
  // ... other settings
};
```

## Security

This template includes several security measures:

- **Security headers** via middleware and Next.js config
- **Environment validation** with Zod
- **Type safety** with strict TypeScript
- **Input validation** with Zod schemas
- **XSS protection** via React's built-in escaping
- **CSRF protection** via SameSite cookies (when auth is added)

## Performance

- **Bundle optimization** with Next.js built-in optimizations
- **Image optimization** with Next.js Image component
- **Font optimization** with next/font
- **Code splitting** automatic with App Router
- **Compression** enabled in production
- **Tree shaking** for unused code elimination

## Testing

Testing setup is ready for implementation:

```bash
# Add your preferred testing framework
bun add -d @testing-library/react @testing-library/jest-dom jest @types/jest
```

## Content Management

Blog posts are managed via MDX files in the `content/` directory:

```markdown
export const frontmatter = {
title: "Your Post Title",
date: "2024-01-01",
author: "Author Name",
summary: "Post summary"
}

# Your Post Content

Your markdown content here...
```

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit your changes: `git commit -m 'Add amazing feature'`
4. Push to the branch: `git push origin feature/amazing-feature`
5. Open a Pull Request to the `dev` branch

## License

This template is not open source. It is proprietary software owned by Stellar Glaze. Use is restricted to internal projects at Stellar Glaze only. Read our [LICENSE](LICENSE) for more information.

## Support

- **Documentation**: Check the [Next.js docs](https://nextjs.org/docs)
- **Issues**: Open an issue on GitHub
- **Discussions**: Use GitHub Discussions for questions

---

Built with <3 by [Stellar Glaze](https://stellar-glaze.com)
